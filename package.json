{"name": "dental-narrator-beta", "version": "1.0.0", "description": "Dental insurance claim narrative generator using RAG", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "init": "ts-node src/index.ts", "test-db": "ts-node src/test-db-connection.ts", "import-csv": "ts-node scripts/import-csv-data.ts", "validate-data": "ts-node scripts/validate-clean-data.ts", "fix-data-quality": "ts-node scripts/fix-data-quality-issues.ts", "test-embeddings": "ts-node scripts/test-embeddings.ts", "generate-embeddings": "ts-node scripts/generate-embeddings.ts", "optimize-vector-indexes": "ts-node scripts/optimize-vector-indexes.ts", "test-vector-search": "ts-node scripts/test-vector-search.ts", "test-agent": "ts-node scripts/test-agent.ts", "test-frontend": "ts-node scripts/test-frontend-simulation.ts", "seed-database": "ts-node scripts/seed-database.ts", "mcp": "mcp-docs-server"}, "keywords": ["dental", "insurance", "claims", "narrative", "mastra", "rag"], "author": "", "license": "MIT", "dependencies": {"@ai-sdk/openai": "^1.0.0", "@mastra/core": "^0.6.3", "@mastra/mcp": "^0.1.0", "@mastra/mcp-docs-server": "^0.0.3", "@mastra/pg": "^0.2.4", "@mastra/rag": "^0.1.12", "@types/pg": "^8.15.4", "ai": "^4.1.66", "dotenv": "^16.3.1", "openai": "^4.104.0", "pg": "^8.16.2", "supabase": "^2.19.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.5", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}